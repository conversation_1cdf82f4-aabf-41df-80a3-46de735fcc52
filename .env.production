#--------------------------------------------------------------------
# PRODUCTION Environment Configuration
#--------------------------------------------------------------------

#--------------------------------------------------------------------
# ENVIRONMENT
#--------------------------------------------------------------------

CI_ENVIRONMENT = production

#--------------------------------------------------------------------
# APP
#--------------------------------------------------------------------

app.baseURL = 'https://halqa.mazharulirfan.com/'
app.indexPage = ''
app.forceGlobalSecureRequests = true
app.appTimezone = 'UTC'

#--------------------------------------------------------------------
# DATABASE CONFIGURATION - PRODUCTION
#--------------------------------------------------------------------

# Centralized database configuration - update these values only
DB_HOSTNAME = localhost
DB_DATABASE = u888771413_halqa
DB_USERNAME = u888771413_halqa_user
DB_PASSWORD = YOUR_SECURE_PASSWORD_HERE
DB_DRIVER = MySQLi
DB_PREFIX =
DB_PORT = 3306
DB_CHARSET = utf8mb4
DB_COLLATION = utf8mb4_general_ci

# Legacy format (automatically set from above)
database.default.hostname = ${DB_HOSTNAME}
database.default.database = ${DB_DATABASE}
database.default.username = ${DB_USERNAME}
database.default.password = ${DB_PASSWORD}
database.default.DBDriver = ${DB_DRIVER}
database.default.DBPrefix = ${DB_PREFIX}
database.default.port = ${DB_PORT}
database.default.charset = ${DB_CHARSET}
database.default.DBCollat = ${DB_COLLATION}

#--------------------------------------------------------------------
# SECURITY
#--------------------------------------------------------------------

# Enable Content Security Policy
app.CSPEnabled = true

# Disable error display for security
display_errors = 0
