#--------------------------------------------------------------------
# PRODUCTION Environment Configuration
#--------------------------------------------------------------------

#--------------------------------------------------------------------
# ENVIRONMENT
#--------------------------------------------------------------------

CI_ENVIRONMENT = production

#--------------------------------------------------------------------
# APP CONFIGURATION - PRODUCTION
#--------------------------------------------------------------------

# Centralized app configuration - update these values only
APP_BASE_URL = https://halqa.mazharulirfan.com/
APP_INDEX_PAGE =
APP_FORCE_HTTPS = true
APP_CSP_ENABLED = true

# Legacy app configuration
app.baseURL = https://halqa.mazharulirfan.com/
app.indexPage =
app.forceGlobalSecureRequests = true
app.appTimezone = UTC

#--------------------------------------------------------------------
# DATABASE CONFIGURATION - PRODUCTION
#--------------------------------------------------------------------

# Centralized database configuration - update these values only
DB_HOSTNAME = localhost
DB_DATABASE = u888771413_halqa
DB_USERNAME = u888771413_halqa_user
DB_PASSWORD = YOUR_SECURE_PASSWORD_HERE
DB_DRIVER = MySQLi
DB_PREFIX =
DB_PORT = 3306
DB_CHARSET = utf8mb4
DB_COLLATION = utf8mb4_general_ci

#--------------------------------------------------------------------
# SECURITY
#--------------------------------------------------------------------

# Enable Content Security Policy
app.CSPEnabled = true

# Disable error display for security
display_errors = 0

#--------------------------------------------------------------------
# LOGGING CONFIGURATION - PRODUCTION
#--------------------------------------------------------------------

# Log only critical errors and above in production
LOG_THRESHOLD = 3

# Log file settings
LOG_FILE_EXTENSION = log
LOG_FILE_PERMISSIONS = 0640
LOG_MAX_SIZE = 10485760

# Disable debug logging in production
LOG_DEBUG_ENABLED = false
