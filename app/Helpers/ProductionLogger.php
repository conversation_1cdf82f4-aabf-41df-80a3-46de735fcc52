<?php

namespace App\Helpers;

class ProductionLogger
{
    /**
     * Log critical application errors
     *
     * @param string $message
     * @param array $context
     */
    public static function critical(string $message, array $context = []): void
    {
        // Sanitize sensitive data before logging
        $sanitizedContext = self::sanitizeContext($context);
        
        log_message('critical', $message, $sanitizedContext);
        
        // In production, you might want to send critical errors to external monitoring
        if (ENVIRONMENT === 'production') {
            self::notifyExternalService('critical', $message, $sanitizedContext);
        }
    }

    /**
     * Log application errors
     *
     * @param string $message
     * @param array $context
     */
    public static function error(string $message, array $context = []): void
    {
        $sanitizedContext = self::sanitizeContext($context);
        log_message('error', $message, $sanitizedContext);
    }

    /**
     * Log database errors with special handling
     *
     * @param string $query
     * @param string $error
     * @param array $context
     */
    public static function databaseError(string $query, string $error, array $context = []): void
    {
        $message = "Database Error: {$error}";
        
        // Don't log the full query in production for security
        if (ENVIRONMENT !== 'production') {
            $context['query'] = $query;
        } else {
            $context['query_hash'] = md5($query); // Log hash instead
        }
        
        self::error($message, $context);
    }

    /**
     * Log authentication events
     *
     * @param string $event
     * @param string $username
     * @param string $ip
     * @param array $context
     */
    public static function authEvent(string $event, string $username, string $ip, array $context = []): void
    {
        $message = "Auth Event: {$event}";
        $context['username'] = $username;
        $context['ip'] = $ip;
        $context['timestamp'] = date('Y-m-d H:i:s');
        
        // Log auth events as info level
        log_message('info', $message, $context);
    }

    /**
     * Log financial transactions (critical for accounting app)
     *
     * @param string $action
     * @param array $transactionData
     * @param string $userId
     */
    public static function financialTransaction(string $action, array $transactionData, string $userId): void
    {
        $message = "Financial Transaction: {$action}";
        
        $context = [
            'user_id' => $userId,
            'action' => $action,
            'timestamp' => date('Y-m-d H:i:s'),
            'transaction_id' => $transactionData['id'] ?? 'unknown',
            'amount' => $transactionData['amount'] ?? 0,
            'member_id' => $transactionData['member_id'] ?? 'unknown',
        ];
        
        // Financial transactions are always critical to log
        log_message('critical', $message, $context);
    }

    /**
     * Sanitize context data to remove sensitive information
     *
     * @param array $context
     * @return array
     */
    private static function sanitizeContext(array $context): array
    {
        $sensitiveKeys = [
            'password',
            'passwd',
            'secret',
            'token',
            'api_key',
            'private_key',
            'credit_card',
            'ssn',
            'social_security',
        ];

        foreach ($sensitiveKeys as $key) {
            if (isset($context[$key])) {
                $context[$key] = '[REDACTED]';
            }
        }

        // Recursively sanitize nested arrays
        foreach ($context as $key => $value) {
            if (is_array($value)) {
                $context[$key] = self::sanitizeContext($value);
            }
        }

        return $context;
    }

    /**
     * Notify external monitoring service (placeholder)
     *
     * @param string $level
     * @param string $message
     * @param array $context
     */
    private static function notifyExternalService(string $level, string $message, array $context): void
    {
        // In a real production environment, you might integrate with:
        // - Sentry
        // - Rollbar
        // - Bugsnag
        // - Custom webhook
        
        // Example webhook notification (uncomment and configure as needed):
        /*
        $payload = [
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'timestamp' => date('c'),
            'environment' => ENVIRONMENT,
            'application' => 'Halqa Accounting',
        ];
        
        // Send to webhook
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'YOUR_WEBHOOK_URL');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_exec($ch);
        curl_close($ch);
        */
    }

    /**
     * Get log file statistics
     *
     * @return array
     */
    public static function getLogStats(): array
    {
        $logPath = WRITEPATH . 'logs/';
        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'oldest_file' => null,
            'newest_file' => null,
        ];

        if (!is_dir($logPath)) {
            return $stats;
        }

        $files = glob($logPath . '*.log');
        $stats['total_files'] = count($files);

        foreach ($files as $file) {
            $stats['total_size'] += filesize($file);
            
            $fileTime = filemtime($file);
            if ($stats['oldest_file'] === null || $fileTime < filemtime($logPath . $stats['oldest_file'])) {
                $stats['oldest_file'] = basename($file);
            }
            if ($stats['newest_file'] === null || $fileTime > filemtime($logPath . $stats['newest_file'])) {
                $stats['newest_file'] = basename($file);
            }
        }

        return $stats;
    }
}
