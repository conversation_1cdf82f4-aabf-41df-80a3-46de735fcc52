<?php

namespace Config;

/**
 * Centralized Database Configuration Helper
 *
 * This class provides a single source of truth for database configuration
 * that can be used across the entire application.
 */
class DatabaseConfig
{
    /**
     * Get database configuration from environment variables
     *
     * @return array Database configuration array
     */
    public static function getConfig(): array
    {
        return [
            'hostname' => env('DB_HOSTNAME'),
            'database' => env('DB_DATABASE'),
            'username' => env('DB_USERNAME'),
            'password' => env('DB_PASSWORD'),
            'DBDriver' => env('DB_DRIVER'),
            'DBPrefix' => env('DB_PREFIX'),
            'port'     => (int) env('DB_PORT'),
            'charset'  => env('DB_CHARSET'),
            'DBCollat' => env('DB_COLLATION'),
        ];
    }

    /**
     * Get specific database configuration value
     *
     * @param string $key Configuration key
     * @param mixed $default Default value if key not found
     * @return mixed Configuration value
     */
    public static function get(string $key, $default = null)
    {
        $config = self::getConfig();
        return $config[$key] ?? $default;
    }

    /**
     * Get database connection string for debugging
     *
     * @return string Connection string (without password)
     */
    public static function getConnectionString(): string
    {
        $config = self::getConfig();
        return sprintf(
            '%s://%s@%s:%d/%s',
            strtolower($config['DBDriver']),
            $config['username'],
            $config['hostname'],
            $config['port'],
            $config['database']
        );
    }

    /**
     * Validate database configuration
     *
     * @return array Array of validation errors (empty if valid)
     */
    public static function validate(): array
    {
        $errors = [];
        $config = self::getConfig();

        if (empty($config['hostname'])) {
            $errors[] = 'Database hostname is required';
        }

        if (empty($config['database'])) {
            $errors[] = 'Database name is required';
        }

        if (empty($config['username'])) {
            $errors[] = 'Database username is required';
        }

        if (!in_array($config['DBDriver'], ['MySQLi', 'Postgre', 'SQLite3', 'SQLSRV', 'OCI8'])) {
            $errors[] = 'Invalid database driver: ' . $config['DBDriver'];
        }

        if ($config['port'] < 1 || $config['port'] > 65535) {
            $errors[] = 'Invalid database port: ' . $config['port'];
        }

        return $errors;
    }
}
