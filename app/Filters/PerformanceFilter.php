<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Config\Performance;

class PerformanceFilter implements FilterInterface
{
    /**
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return RequestInterface|void
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Enable output compression if configured
        $performance = config('Performance');
        
        if ($performance->compressionEnabled && !ob_get_level()) {
            if (extension_loaded('zlib') && !ini_get('zlib.output_compression')) {
                ob_start('ob_gzhandler');
            }
        }
    }

    /**
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return ResponseInterface|void
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        $performance = config('Performance');
        
        // Add cache headers for static assets
        if ($performance->cacheHeaders) {
            $uri = $request->getUri()->getPath();
            
            // Check if this is a static asset
            if (preg_match('/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/i', $uri)) {
                $response->setHeader('Cache-Control', 'public, max-age=' . $performance->cacheDuration);
                $response->setHeader('Expires', gmdate('D, d M Y H:i:s', time() + $performance->cacheDuration) . ' GMT');
            }
        }
        
        // Minify HTML output
        if ($performance->minifyHTML && $response->getHeaderLine('Content-Type') === 'text/html') {
            $body = $response->getBody();
            if ($body) {
                $minified = $this->minifyHTML($body);
                $response->setBody($minified);
            }
        }
        
        // Add performance headers
        $response->setHeader('X-Content-Type-Options', 'nosniff');
        $response->setHeader('X-Frame-Options', 'DENY');
        $response->setHeader('X-XSS-Protection', '1; mode=block');
        
        return $response;
    }

    /**
     * Minify HTML content
     *
     * @param string $html
     * @return string
     */
    private function minifyHTML(string $html): string
    {
        // Remove HTML comments (except IE conditional comments)
        $html = preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).)*-->/s', '', $html);
        
        // Remove unnecessary whitespace
        $html = preg_replace('/\s+/', ' ', $html);
        $html = preg_replace('/>\s+</', '><', $html);
        
        // Remove whitespace around block elements
        $html = preg_replace('/\s*(<\/?(div|p|h[1-6]|ul|ol|li|table|tr|td|th|thead|tbody|tfoot|form|fieldset|legend|label|input|textarea|select|option|button)[^>]*>)\s*/', '$1', $html);
        
        return trim($html);
    }
}
