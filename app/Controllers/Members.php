<?php

namespace App\Controllers;

use App\Models\MemberModel;
use App\Models\CommitmentModel;
use App\Models\PaymentModel;
use App\Models\CollectionSummaryModel;
use App\Libraries\PaginationHelper;
use CodeIgniter\RESTful\ResourceController;

class Members extends ResourceController
{
    protected $memberModel;
    protected $commitmentModel;
    protected $paymentModel;
    protected $summaryModel;

    public function __construct()
    {
        $this->memberModel = new MemberModel();
        $this->commitmentModel = new CommitmentModel();
        $this->paymentModel = new PaymentModel();
        $this->summaryModel = new CollectionSummaryModel();
    }

    /**
     * Display a list of all members with pagination
     *
     * @return mixed
     */
    public function index()
    {
        // Get pagination parameters
        $paginationParams = PaginationHelper::getPaginationParams($this->request);
        $search = $this->request->getGet('search') ?? '';
        $orderBy = $this->request->getGet('order_by') ?? 'balance';
        $orderDir = $this->request->getGet('order_dir') ?? 'DESC';

        // Get paginated members with balance information
        $members = $this->memberModel->getPaginatedMembersWithBalance(
            $paginationParams['per_page'],
            $paginationParams['offset'],
            $search,
            $orderBy,
            $orderDir
        );

        // Get total count for pagination
        $totalMembers = $this->memberModel->getTotalMembersCount($search);

        // Create pagination data
        $paginationData = PaginationHelper::createPaginationData(
            $totalMembers,
            $paginationParams['page'],
            $paginationParams['per_page'],
            base_url('members')
        );

        $data = [
            'title' => 'Members',
            'members' => $members,
            'pagination' => $paginationData,
            'search' => $search,
            'order_by' => $orderBy,
            'order_dir' => $orderDir,
            'total_members' => $totalMembers
        ];

        return view('members/index', $data);
    }

    /**
     * Display the form to create a new member
     *
     * @return mixed
     */
    public function new()
    {
        $data = [
            'title' => 'Add New Member'
        ];

        return view('members/create', $data);
    }

    /**
     * Create a new member
     *
     * @return mixed
     */
    public function create()
    {
        $rules = $this->memberModel->getValidationRules();

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $memberId = $this->memberModel->insert([
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'post_office' => $this->request->getPost('post_office'),
            'old_reference' => $this->request->getPost('old_reference'),
            'phone' => $this->request->getPost('phone'),
            'whatsapp_number' => $this->request->getPost('whatsapp_number'),
            'address' => $this->request->getPost('address'),
            'join_date' => $this->request->getPost('join_date'),
            'status' => 'active'
        ]);

        if (!$memberId) {
            return redirect()->back()->withInput()->with('error', 'Failed to create member');
        }

        return redirect()->to('/members')->with('message', 'Member created successfully');
    }

    /**
     * Display a specific member
     *
     * @param int $id
     * @return mixed
     */
    public function show($id = null)
    {
        $member = $this->memberModel->getMemberWithDetails($id);

        if (!$member) {
            return redirect()->to('/members')->with('error', 'Member not found');
        }

        // Calculate balance for each commitment
        $commitmentModel = new \App\Models\CommitmentModel();
        foreach ($member['commitments'] as &$commitment) {
            $balance = $commitmentModel->calculateCommitmentBalance($commitment['commitment_id']);
            $commitment['total_due'] = $balance['totalDue'];
            $commitment['total_paid'] = $balance['totalPaid'];
            $commitment['balance'] = $balance['balance'];

            // Add calculation details for display
            $amount = $commitment['amount'];
            $frequency = $commitment['frequency'];
            $startDate = new \DateTime($commitment['start_date']);
            $endDate = !empty($commitment['end_date']) ? new \DateTime($commitment['end_date']) : new \DateTime(); // Use today's date if no end date

            // Calculate the number of periods
            if ($frequency == 'one-time') {
                $commitment['periods'] = 1;
            } else {
                $interval = $startDate->diff($endDate);
                $totalMonths = ($interval->y * 12) + $interval->m + ($interval->d > 0 ? 1 : 0);

                switch ($frequency) {
                    case 'monthly':
                        $commitment['periods'] = $totalMonths;
                        break;
                    case 'quarterly':
                        $commitment['periods'] = ceil($totalMonths / 3);
                        break;
                    case 'yearly':
                        $commitment['periods'] = ceil($totalMonths / 12);
                        break;
                    default:
                        $commitment['periods'] = 1;
                }
            }

            // Calculate the number of paid periods
            if ($amount > 0) {
                $commitment['paid_periods'] = floor($balance['totalPaid'] / $amount);
            } else {
                $commitment['paid_periods'] = 0;
            }

            // Calculate the number of remaining periods
            $commitment['remaining_periods'] = max(0, $commitment['periods'] - $commitment['paid_periods']);
        }

        $data = [
            'title' => 'Member Details: ' . $member['name'],
            'member' => $member
        ];

        return view('members/show', $data);
    }

    /**
     * Display the form to edit a member
     *
     * @param int $id
     * @return mixed
     */
    public function edit($id = null)
    {
        $member = $this->memberModel->find($id);

        if (!$member) {
            return redirect()->to('/members')->with('error', 'Member not found');
        }

        $data = [
            'title' => 'Edit Member: ' . $member['name'],
            'member' => $member
        ];

        return view('members/edit', $data);
    }

    /**
     * Update a member
     *
     * @param int $id
     * @return mixed
     */
    public function update($id = null)
    {
        $member = $this->memberModel->find($id);

        if (!$member) {
            return redirect()->to('/members')->with('error', 'Member not found');
        }

        $rules = $this->memberModel->getValidationRules();

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $this->memberModel->update($id, [
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'post_office' => $this->request->getPost('post_office'),
            'old_reference' => $this->request->getPost('old_reference'),
            'phone' => $this->request->getPost('phone'),
            'whatsapp_number' => $this->request->getPost('whatsapp_number'),
            'address' => $this->request->getPost('address'),
            'join_date' => $this->request->getPost('join_date'),
            'status' => $this->request->getPost('status')
        ]);

        return redirect()->to('/members')->with('message', 'Member updated successfully');
    }

    /**
     * Delete a member
     *
     * @param int $id
     * @return mixed
     */
    public function delete($id = null)
    {
        $member = $this->memberModel->find($id);

        if (!$member) {
            return redirect()->to('/members')->with('error', 'Member not found');
        }

        $this->memberModel->delete($id);

        return redirect()->to('/members')->with('message', 'Member deleted successfully');
    }
}
