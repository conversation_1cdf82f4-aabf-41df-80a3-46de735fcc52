<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Commitment Report</h1>
    <a href="<?= site_url('reports') ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
    </a>
</div>

<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0">Filter Options</h5>
    </div>
    <div class="card-body">
        <form action="<?= site_url('reports/commitment-report') ?>" method="get" class="row g-3">
            <div class="col-md-4">
                <label for="frequency" class="form-label">Frequency</label>
                <select class="form-select" id="frequency" name="frequency">
                    <option value="">All Frequencies</option>
                    <option value="monthly" <?= $frequency == 'monthly' ? 'selected' : '' ?>>Monthly</option>
                    <option value="quarterly" <?= $frequency == 'quarterly' ? 'selected' : '' ?>>Quarterly</option>
                    <option value="yearly" <?= $frequency == 'yearly' ? 'selected' : '' ?>>Yearly</option>
                    <option value="one-time" <?= $frequency == 'one-time' ? 'selected' : '' ?>>One-time</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="active" <?= $status == 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= $status == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                    <option value="all" <?= $status == 'all' ? 'selected' : '' ?>>All</option>
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-filter"></i> Apply Filters
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            Commitment Report
            <?php if ($frequency): ?>
                - <?= ucfirst($frequency) ?> Frequency
            <?php endif; ?>
            - <?= ucfirst($status) ?> Commitments
        </h5>
        <div>
            <button onclick="window.print()" class="btn btn-sm btn-light">
                <i class="fas fa-print"></i> Print Report
            </button>
            <a href="<?= site_url('reports/export-commitment-report?' . http_build_query($_GET)) ?>" class="btn btn-sm btn-light">
                <i class="fas fa-file-excel"></i> Export to Excel
            </a>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($commitments)): ?>
            <div class="alert alert-info">
                No commitments found matching the selected criteria.
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Member</th>
                            <th>Unit Amount</th>
                            <th>Frequency</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Total Amount</th>
                            <th>Member Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($commitments as $commitment): ?>
                            <tr>
                                <td><?= $commitment['commitment_id'] ?></td>
                                <td>
                                    <a href="<?= site_url('members/show/' . $commitment['member_id']) ?>">
                                        <?= $commitment['member_name'] ?>
                                    </a>
                                </td>
                                <td><?= format_currency_with_decimals($commitment['amount']) ?></td>
                                <td><?= ucfirst($commitment['frequency']) ?></td>
                                <td><?= date('d M Y', strtotime($commitment['start_date'])) ?></td>
                                <td>
                                    <?= $commitment['end_date'] ? date('d M Y', strtotime($commitment['end_date'])) : 'Ongoing' ?>
                                </td>
                                <td class="fw-bold">
                                    <?= format_currency_with_decimals($commitment['calculated_amount']) ?>
                                    <?php if (isset($commitment['periods']) && $commitment['periods'] > 1): ?>
                                        <small class="text-muted">
                                            (<?= $commitment['amount'] ?> × <?= $commitment['periods'] ?> <?= $commitment['frequency'] == 'monthly' ? 'months' : ($commitment['frequency'] == 'quarterly' ? 'quarters' : 'years') ?>)
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($commitment['member_status'] == 'active'): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= site_url('commitments/show/' . $commitment['commitment_id']) ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= site_url('members/show/' . $commitment['member_id']) ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="View Member">
                                            <i class="fas fa-user"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-dark">
                            <th colspan="6">TOTAL</th>
                            <th><?= number_format($totalAmount, 2) ?></th>
                            <th colspan="2"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<style type="text/css" media="print">
    @media print {
        .sidebar, .navbar, .card-header button, .card-header a, .btn, .dataTables_filter, .dataTables_length, .dataTables_paginate, .dataTables_info {
            display: none !important;
        }
        .content {
            margin-left: 0 !important;
            padding: 0 !important;
        }
        .card {
            border: none !important;
        }
        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
        }
    }
</style>

<?= $this->endSection() ?>
