<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1><?= $title ?></h1>
            <p class="text-muted">Active members who currently have no ongoing commitments. These members may need attention to renew or create new commitments.</p>
        </div>
        <div class="d-flex">
            <a href="<?= site_url('reports') ?>" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
            <a href="<?= site_url('commitments/new') ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add New Commitment
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-warning text-dark">
            <h5 class="card-title mb-0"><i class="fas fa-exclamation-circle"></i> Members Without Active Commitments</h5>
        </div>
        <div class="card-body">
            <?php if (empty($members)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> All active members currently have commitments.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover datatable">
                        <thead class="table-dark">
                            <tr>
                                <th>Name</th>
                                <th class="text-center">Contact</th>
                                <th>Email</th>
                                <th>Join Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($members as $member): ?>
                                <tr>
                                    <td>
                                        <a href="<?= site_url('members/show/' . $member['member_id']) ?>">
                                            <?= esc($member['name']) ?>
                                        </a>
                                    </td>
                                    <td class="text-center">
                                        <div class="d-flex justify-content-center gap-3">
                                            <?php if (!empty($member['whatsapp_number'])): ?>
                                                <a href="https://wa.me/<?= preg_replace('/[^0-9]/', '', $member['whatsapp_number']) ?>?text=Hello%20<?= urlencode($member['name']) ?>,%20we%20noticed%20you%20don't%20have%20any%20active%20commitments." target="_blank" class="text-decoration-none" data-bs-toggle="tooltip" title="WhatsApp">
                                                    <i class="fab fa-whatsapp text-success fa-lg"></i>
                                                </a>
                                            <?php endif; ?>

                                            <?php if (!empty($member['phone'])): ?>
                                                <a href="tel:<?= esc($member['phone']) ?>" class="text-decoration-none" data-bs-toggle="tooltip" title="Call">
                                                    <i class="fas fa-phone-alt text-success fa-lg"></i>
                                                </a>
                                            <?php endif; ?>

                                            <?php if (empty($member['phone']) && empty($member['whatsapp_number'])): ?>
                                                <span class="text-muted">—</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if (!empty($member['email'])): ?>
                                            <a href="mailto:<?= esc($member['email']) ?>"><?= esc($member['email']) ?></a>
                                        <?php else: ?>
                                            <span class="text-muted">Not provided</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= date('M d, Y', strtotime($member['join_date'])) ?></td>
                                    <td>
                                        <a href="<?= site_url('commitments/create/' . $member['member_id']) ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Add Commitment">
                                            <i class="fas fa-plus"></i> Add
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
