<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Organization Accounting System' ?></title>

    <!-- Performance optimizations -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://code.jquery.com">
    <link rel="dns-prefetch" href="https://cdn.datatables.net">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?= base_url('assets/css/custom.css') ?>">

    <style>
        /* Sidebar styles */
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
            padding-top: 20px;
            transition: all 0.3s;
        }

        .sidebar .nav-link {
            color: #333;
            padding: 10px 20px;
            margin-bottom: 5px;
            border-radius: 4px;
        }

        .sidebar .nav-link:hover {
            background-color: #e9ecef;
        }

        .sidebar .nav-link.active {
            background-color: #0d6efd;
            color: white;
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .content {
            padding: 20px;
            transition: all 0.3s;
        }

        .card {
            margin-bottom: 20px;
        }

        /* Mobile sidebar styles */
        @media (max-width: 991.98px) {
            .sidebar {
                position: fixed;
                top: 56px;
                left: 0;
                width: 80%;
                height: calc(100vh - 56px);
                z-index: 1030;
                overflow-y: auto;
                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .content {
                width: 100%;
                margin-left: 0;
            }

            /* Add backdrop when sidebar is open */
            .sidebar-backdrop {
                position: fixed;
                top: 56px;
                left: 0;
                width: 100%;
                height: calc(100vh - 56px);
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1020;
                display: none;
            }

            .sidebar-backdrop.show {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?= site_url('/') ?>">Organization Accounting System</a>

            <!-- Sidebar toggle button (visible only on mobile) -->
            <button class="navbar-toggler me-2 d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle sidebar">
                <i class="fas fa-bars"></i>
            </button>

            <!-- Main navbar toggle button -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= site_url('/reports') ?>">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user"></i> <?= session()->get('username') ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                            <li><a class="dropdown-item" href="<?= site_url('auth/change-password') ?>"><i class="fas fa-key"></i> Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?= site_url('auth/logout') ?>"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-2 d-lg-block collapse sidebar" id="sidebarMenu">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?= uri_string() == 'reports' ? 'active' : '' ?>" href="<?= site_url('/reports') ?>">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= (uri_string() == 'members' || strpos(uri_string(), 'members/') === 0) ? 'active' : '' ?>" href="<?= site_url('/members') ?>">
                                <i class="fas fa-users"></i> Members
                            </a>
                        </li>
                        <!-- Commitments and Payments links removed as requested -->
                        <li class="nav-item">
                            <a class="nav-link <?= uri_string() == 'reports/collection-summary' ? 'active' : '' ?>" href="<?= site_url('/reports/collection-summary') ?>">
                                <i class="fas fa-chart-bar"></i> Collection Summary
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= uri_string() == 'reports/payment-history' ? 'active' : '' ?>" href="<?= site_url('/reports/payment-history') ?>">
                                <i class="fas fa-history"></i> Payment History
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link <?= uri_string() == 'reports/members-without-commitments' ? 'active' : '' ?>" href="<?= site_url('/reports/members-without-commitments') ?>">
                                <i class="fas fa-exclamation-circle"></i> Inactive Commitments
                            </a>
                        </li>

                        <!-- Outstanding Balances and Commitment Report links removed as requested -->
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-10 content">
                <?php if (session()->getFlashdata('message')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?= session()->getFlashdata('message') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= session()->getFlashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($errors) && $errors): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <ul>
                            <?php foreach ($errors as $error): ?>
                                <li><?= esc($error) ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?= $this->renderSection('content') ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS - Async loading for better performance -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js" async></script>

    <!-- jQuery - Defer loading -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js" defer></script>

    <!-- DataTables JS - Load only when needed -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js" defer></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js" defer></script>

    <script>
        $(document).ready(function() {
            // Initialize DataTables
            $('.datatable').DataTable({
                responsive: true,
                order: [[0, 'desc']],
                // Swap the positions of "Show X entries" and "Search"
                dom: '<"row"<"col-sm-6"f><"col-sm-6"l>><"row"<"col-sm-12"tr>><"row"<"col-sm-5"i><"col-sm-7"p>>'
            });

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });

            // Mobile sidebar functionality
            function setupMobileSidebar() {
                // Create backdrop element if it doesn't exist
                if ($('.sidebar-backdrop').length === 0) {
                    $('body').append('<div class="sidebar-backdrop"></div>');
                }

                // Handle sidebar toggle
                $('.sidebar-backdrop').on('click', function() {
                    $('#sidebarMenu').collapse('hide');
                    $(this).removeClass('show');
                });

                // Show backdrop when sidebar is shown
                $('#sidebarMenu').on('show.bs.collapse', function () {
                    $('.sidebar-backdrop').addClass('show');
                });

                // Hide backdrop when sidebar is hidden
                $('#sidebarMenu').on('hide.bs.collapse', function () {
                    $('.sidebar-backdrop').removeClass('show');
                });

                // Close sidebar when a menu item is clicked on mobile
                $('#sidebarMenu .nav-link').on('click', function() {
                    if (window.innerWidth < 992) {
                        $('#sidebarMenu').collapse('hide');
                        $('.sidebar-backdrop').removeClass('show');
                    }
                });
            }

            setupMobileSidebar();

            // Re-setup on window resize
            $(window).on('resize', function() {
                setupMobileSidebar();
            });
        });
    </script>

    <?= $this->renderSection('scripts') ?>
</body>
</html>
