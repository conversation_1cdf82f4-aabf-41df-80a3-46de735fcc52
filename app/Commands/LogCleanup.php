<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class LogCleanup extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Maintenance';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'log:cleanup';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Clean up old log files and rotate large logs';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'log:cleanup [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [
        '--days' => 'Number of days to keep logs (default: 30)',
        '--dry-run' => 'Show what would be deleted without actually deleting',
    ];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $daysToKeep = CLI::getOption('days') ?? 30;
        $dryRun = CLI::getOption('dry-run') ?? false;

        CLI::write('Log Cleanup Utility', 'yellow');
        CLI::write('==================', 'yellow');

        $logPath = WRITEPATH . 'logs/';
        
        if (!is_dir($logPath)) {
            CLI::error('Log directory not found: ' . $logPath);
            return;
        }

        $cutoffDate = time() - ($daysToKeep * 24 * 60 * 60);
        $deletedCount = 0;
        $totalSize = 0;

        CLI::write("Scanning for log files older than {$daysToKeep} days...", 'green');

        $files = glob($logPath . '*.log');
        
        foreach ($files as $file) {
            $fileTime = filemtime($file);
            $fileSize = filesize($file);
            
            if ($fileTime < $cutoffDate) {
                $totalSize += $fileSize;
                
                if ($dryRun) {
                    CLI::write("Would delete: " . basename($file) . " (" . $this->formatBytes($fileSize) . ")", 'yellow');
                } else {
                    if (unlink($file)) {
                        CLI::write("Deleted: " . basename($file) . " (" . $this->formatBytes($fileSize) . ")", 'red');
                        $deletedCount++;
                    } else {
                        CLI::error("Failed to delete: " . basename($file));
                    }
                }
            }
        }

        // Rotate large current log files
        $this->rotateLargeLogs($logPath, $dryRun);

        if ($dryRun) {
            CLI::write("\nDry run completed. Would delete {$deletedCount} files totaling " . $this->formatBytes($totalSize), 'cyan');
        } else {
            CLI::write("\nCleanup completed. Deleted {$deletedCount} files totaling " . $this->formatBytes($totalSize), 'green');
        }
    }

    /**
     * Rotate large log files
     *
     * @param string $logPath
     * @param bool $dryRun
     */
    private function rotateLargeLogs(string $logPath, bool $dryRun): void
    {
        $maxSize = 10 * 1024 * 1024; // 10MB
        $currentLogFile = $logPath . 'log-' . date('Y-m-d') . '.log';

        if (file_exists($currentLogFile) && filesize($currentLogFile) > $maxSize) {
            $rotatedFile = $logPath . 'log-' . date('Y-m-d-H-i-s') . '.log';
            
            if ($dryRun) {
                CLI::write("Would rotate large log file: " . basename($currentLogFile), 'yellow');
            } else {
                if (rename($currentLogFile, $rotatedFile)) {
                    CLI::write("Rotated large log file: " . basename($currentLogFile), 'green');
                } else {
                    CLI::error("Failed to rotate: " . basename($currentLogFile));
                }
            }
        }
    }

    /**
     * Format bytes to human readable format
     *
     * @param int $bytes
     * @return string
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
