<?php

namespace App\Models;

use CodeIgniter\Model;

class PaymentModel extends Model
{
    protected $table = 'payments';
    protected $primaryKey = 'payment_id';

    protected $useAutoIncrement = true;
    protected $returnType = 'array';

    protected $allowedFields = [
        'member_id',
        'commitment_id',
        'amount',
        'payment_date',
        'receipt_number',
        'receipt_book_number',
        'payment_method',
        'notes',
        'payment_periods'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'member_id' => 'required|integer',
        'commitment_id' => 'permit_empty|integer',
        'amount' => 'required|numeric',
        'payment_date' => 'required|valid_date',
        'receipt_number' => 'required',
        'receipt_book_number' => 'required',
        'payment_method' => 'required|in_list[cash,check,bank_transfer,other]'
    ];

    protected $validationMessages = [
        'member_id' => [
            'required' => 'Member ID is required',
            'integer' => 'Member ID must be an integer'
        ],
        'commitment_id' => [
            'integer' => 'Commitment ID must be an integer'
        ],
        'amount' => [
            'required' => 'Amount is required',
            'numeric' => 'Amount must be a number'
        ],
        'payment_date' => [
            'required' => 'Payment date is required',
            'valid_date' => 'Payment date must be a valid date'
        ],
        'receipt_number' => [
            'required' => 'Receipt number is required'
        ],
        'receipt_book_number' => [
            'required' => 'Receipt Book number is required'
        ],
        'payment_method' => [
            'required' => 'Payment method is required',
            'in_list' => 'Payment method must be one of: cash, check, bank_transfer, other'
        ]
    ];

    protected $skipValidation = false;

    /**
     * Generate a unique receipt number
     *
     * @return string
     */
    public function generateReceiptNumber()
    {
        $prefix = 'RCPT';
        $year = date('Y');
        $month = date('m');

        // Get the last receipt number
        $lastReceipt = $this->select('receipt_number')
            ->like('receipt_number', $prefix . $year . $month)
            ->orderBy('payment_id', 'DESC')
            ->first();

        if ($lastReceipt) {
            // Extract the sequence number and increment
            $sequence = intval(substr($lastReceipt['receipt_number'], -4)) + 1;
        } else {
            $sequence = 1;
        }

        // Format the new receipt number
        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get payments with member details
     *
     * @return array
     */
    public function getPaymentsWithMemberDetails()
    {
        $db = \Config\Database::connect();

        $query = $db->table('payments p')
            ->select('p.*, m.name as member_name, c.amount as commitment_amount, c.frequency as commitment_frequency')
            ->join('members m', 'm.member_id = p.member_id')
            ->join('commitments c', 'c.commitment_id = p.commitment_id', 'left')
            ->orderBy('p.payment_date', 'DESC')
            ->get();

        return $query->getResultArray();
    }

    /**
     * Get payments for a specific member
     *
     * @param int $memberId
     * @return array
     */
    public function getMemberPayments($memberId)
    {
        return $this->where('member_id', $memberId)
            ->orderBy('payment_date', 'DESC')
            ->findAll();
    }

    /**
     * Calculate total payments for a member
     *
     * @param int $memberId
     * @return float
     */
    public function calculateTotalPayments($memberId)
    {
        $result = $this->selectSum('amount')
            ->where('member_id', $memberId)
            ->first();

        return $result['amount'] ?? 0;
    }
}
