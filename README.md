# Financial Tracking Application

A comprehensive financial tracking system built with CodeIgniter 4 for managing member commitments, payments, and financial reports.

## 🚀 **Starting and Stopping the Application**

### **Starting the Application**

1. **Start the Database Server (MySQL)**
   ```bash
   # On macOS with Homebrew
   brew services start mysql

   # On Ubuntu/Debian
   sudo systemctl start mysql

   # On Windows (XAMPP)
   Start XAMPP Control Panel → Start MySQL
   ```

2. **Start the Web Server**
   ```bash
   # Navigate to project directory
   cd /path/to/org_accounting

   # Start PHP built-in server
   php spark serve

   # Or specify port
   php spark serve --port=8082
   ```

3. **Access the Application**
   - Open browser: `http://localhost:8080` (or your specified port)
   - Login with: Username: `boss`, Password: `admin123`

### **Stopping the Application**

1. **Stop the Web Server**
   ```bash
   # Press Ctrl+C in the terminal where PHP server is running
   ```

2. **Stop the Database Server**
   ```bash
   # On macOS with Homebrew
   brew services stop mysql

   # On Ubuntu/Debian
   sudo systemctl stop mysql

   # On Windows (XAMPP)
   XAMPP Control Panel → Stop MySQL
   ```

## 🛠️ **Complete Setup Guide for New System**

### **Prerequisites**

1. **PHP 8.0 or higher**
2. **MySQL 5.7 or higher**
3. **Composer** (PHP dependency manager)
4. **Git** (optional, for version control)

### **Step 1: Install Required Software**

#### **On macOS:**
```bash
# Install Homebrew (if not installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install PHP, MySQL, and Composer
brew install php mysql composer
```

#### **On Ubuntu/Debian:**
```bash
# Update package list
sudo apt update

# Install PHP and extensions
sudo apt install php8.1 php8.1-cli php8.1-mysql php8.1-mbstring php8.1-xml php8.1-curl php8.1-zip php8.1-intl

# Install MySQL
sudo apt install mysql-server

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

#### **On Windows:**
1. **Download XAMPP**: https://www.apachefriends.org/download.html
2. **Install XAMPP** (includes PHP, MySQL, Apache)
3. **Download Composer**: https://getcomposer.org/download/
4. **Install Composer** following the installer

### **Step 2: Clone/Copy the Project**

```bash
# Option 1: If using Git
git clone <repository-url> org_accounting
cd org_accounting

# Option 2: Copy files manually
# Copy the entire org_accounting folder to your desired location
```

### **Step 3: Install Dependencies**

```bash
cd org_accounting
composer install
```

### **Step 4: Database Setup**

1. **Start MySQL Server**
   ```bash
   # macOS
   brew services start mysql

   # Ubuntu/Debian
   sudo systemctl start mysql

   # Windows (XAMPP)
   Start MySQL from XAMPP Control Panel
   ```

2. **Create Database**
   ```bash
   mysql -u root -p
   ```
   ```sql
   CREATE DATABASE org_accounting;
   EXIT;
   ```

3. **Import Database Structure**
   ```bash
   mysql -u root -p org_accounting < database_backup.sql
   ```

### **Step 5: Configure Environment**

1. **Copy Environment File**
   ```bash
   cp env .env
   ```

2. **Edit Database Configuration**
   ```bash
   nano .env
   ```

   Update these lines:
   ```env
   database.default.hostname = localhost
   database.default.database = org_accounting
   database.default.username = root
   database.default.password = your_mysql_password
   database.default.DBDriver = MySQLi
   database.default.port = 3306
   ```

3. **Update Session Configuration**
   ```env
   session.driver = 'App\Libraries\CustomFileHandler'
   session.savePath = '/full/path/to/org_accounting/writable/session'
   ```

### **Step 6: Set Permissions**

```bash
# Make writable directories writable
chmod -R 755 writable/
chmod -R 755 public/

# On Linux/macOS, you might need:
sudo chown -R www-data:www-data writable/
# Or for your user:
sudo chown -R $USER:$USER writable/
```

### **Step 7: Run Database Migrations (if needed)**

```bash
php spark migrate
```

### **Step 8: Start the Application**

```bash
php spark serve --port=8082
```

## 📁 **Project Structure Overview**

```
org_accounting/
├── app/
│   ├── Controllers/         # Application controllers
│   ├── Models/             # Database models
│   ├── Views/              # HTML templates
│   ├── Libraries/          # Custom libraries
│   ├── Helpers/            # Helper functions
│   └── Config/             # Configuration files
├── public/                 # Web accessible files
│   ├── assets/            # CSS, JS, images
│   └── index.php          # Entry point
├── writable/              # Logs, cache, sessions
│   ├── logs/
│   ├── cache/
│   └── session/
├── vendor/                # Composer dependencies
├── .env                   # Environment configuration
├── composer.json          # PHP dependencies
└── spark                  # CodeIgniter CLI tool
```

## 🔧 **Configuration Files to Update**

### **1. Database Configuration (.env)**
```env
database.default.hostname = localhost
database.default.database = org_accounting
database.default.username = root
database.default.password = your_password
```

### **2. Base URL (.env)**
```env
app.baseURL = 'http://localhost:8082/'
```

### **3. Session Configuration (.env)**
```env
session.driver = 'App\Libraries\CustomFileHandler'
session.savePath = '/absolute/path/to/writable/session'
```

## 🚨 **Troubleshooting Common Issues**

### **1. Database Connection Error**
- Check MySQL is running: `mysql -u root -p`
- Verify database exists: `SHOW DATABASES;`
- Check credentials in `.env` file

### **2. Session Errors**
- Ensure `writable/session/` directory exists and is writable
- Check session path in `.env` is absolute path
- Verify permissions: `chmod 755 writable/session/`

### **3. Permission Errors**
```bash
# Fix permissions
chmod -R 755 writable/
chmod -R 755 public/
```

### **4. Composer Dependencies**
```bash
# Reinstall dependencies
rm -rf vendor/
composer install
```

### **5. PHP Extensions Missing**
```bash
# Check required extensions
php -m | grep -E "mysqli|mbstring|intl|json|xml"
```

## 📋 **Production Deployment Checklist**

### **1. Server Requirements**
- ✅ PHP 8.0+ with required extensions
- ✅ MySQL 5.7+ or MariaDB 10.3+
- ✅ Web server (Apache/Nginx)
- ✅ SSL certificate (recommended)

### **2. Security Configuration**
```env
# .env for production
CI_ENVIRONMENT = production
app.forceGlobalSecureRequests = true
```

### **3. Database Backup**
```bash
# Create backup
mysqldump -u root -p org_accounting > backup_$(date +%Y%m%d).sql

# Restore backup
mysql -u root -p org_accounting < backup_20241201.sql
```

### **4. File Permissions (Production)**
```bash
# Secure permissions
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod -R 755 writable/
chmod 644 .env
```

## 🔄 **Backup and Restore**

### **Database Backup**
```bash
# Create backup
mysqldump -u root -p org_accounting > org_accounting_backup.sql

# Restore backup
mysql -u root -p org_accounting < org_accounting_backup.sql
```

### **File Backup**
```bash
# Backup entire project
tar -czf org_accounting_backup.tar.gz org_accounting/

# Restore project
tar -xzf org_accounting_backup.tar.gz
```

## 📞 **Quick Start Commands**

```bash
# Start everything
brew services start mysql          # Start MySQL
cd /path/to/org_accounting         # Navigate to project
php spark serve --port=8082        # Start web server

# Stop everything
# Ctrl+C                           # Stop web server
brew services stop mysql           # Stop MySQL
```

## 🎯 **Application Features**

### **Member Management**
- ✅ Add, edit, and manage member information
- ✅ Track member status (active/inactive)
- ✅ Contact information with WhatsApp integration
- ✅ Member financial summaries

### **Commitment Tracking**
- ✅ Monthly and one-time commitments
- ✅ Flexible date ranges
- ✅ Automatic calculation of total amounts
- ✅ Payment period tracking

### **Payment Processing**
- ✅ Record payments with receipt generation
- ✅ Multiple payment methods support
- ✅ Automatic balance calculations
- ✅ Payment history tracking

### **Financial Reports**
- ✅ Outstanding balances report
- ✅ Collection summary with filters
- ✅ Payment history and trends
- ✅ Dashboard with key metrics
- ✅ Interactive charts and graphs

### **Performance Optimizations**
- ✅ Pagination for large datasets (1000+ members)
- ✅ Database indexing for fast queries
- ✅ Efficient search functionality
- ✅ Optimized for mobile devices

### **Currency Support**
- ✅ Indian Rupees (₹) display throughout
- ✅ Consistent currency formatting
- ✅ Amount in words conversion

## 🔐 **Default Login Credentials**

- **Username**: `boss`
- **Password**: `admin123`

> **Note**: Change these credentials after first login for security.

## 📱 **Browser Compatibility**

- ✅ Chrome (recommended)
- ✅ Firefox
- ✅ Safari
- ✅ Edge
- ✅ Mobile browsers (responsive design)

## 🆘 **Support**

If you encounter any issues:

1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Ensure database connection is working
4. Check file permissions
5. Review error logs in `writable/logs/`

---

**Your financial tracking application is now ready to run on any system!** 🎉
